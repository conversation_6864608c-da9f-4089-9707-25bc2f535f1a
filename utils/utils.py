import os.path
import random

import re

from conf.config import settings

sensitive_words_path = os.path.join(settings.CONF_DIR, 'sensitive_words.txt')

with open(sensitive_words_path, 'r', encoding='utf-8') as f:
    sensitive_words = f.read()


def get_seed() -> int:
    """
    获取随机seed
    Returns:

    """
    return random.randrange(1, 999999999)


def get_sensitive_word(input_text: str):
    # 将敏感词列表分割成单独的词条
    sensitive_words_list = sensitive_words.strip().split('\n')

    # 构建正则表达式模式
    # 处理每个敏感词，将其转义并允许中间插入任意字符
    pattern = '|'.join(r''.join(re.escape(char) + r'[\s\w\d]{0,3}?' for char in word) for word in sensitive_words_list)
    regex = re.compile(pattern, re.IGNORECASE)

    results = regex.findall(input_text)
    return results
