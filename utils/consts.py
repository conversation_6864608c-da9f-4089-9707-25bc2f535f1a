import random

DEFAULT_CFG = 6
DEFAULT_DENOISE = 1
DEFAULT_STEPS = 25
DEFAULT_BATCH_SIZE = 1
DEFAULT_HEIGHT = 512
DEFAULT_WIDTH = 512
DEFAULT_SAMPLER_NAME = 'euler_ancestral'
DEFAULT_SCHEDULER = 'normal'

DEFAULT_NEGATIVE_TEXT = "lowres, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, username, blurry, artist name"

SAMPLER_LIST = [
    "euler", "euler_ancestral", "heun", "heunpp2", "dpm_2", "dpm_2_ancestral", "lms", "dpm_fast", "dpm_adaptive",
    "dpmpp_2s_ancestral", "dpmpp_sde", "dpmpp_sde_gpu", "dpmpp_2m", "dpmpp_2m_sde", "dpmpp_2m_sde_gpu",
    "dpmpp_3m_sde", "dpmpp_3m_sde_gpu", "ddpm", "lcm"
]

SCHEDULER_LIST = ["normal", "karras", "exponential", "sgm_uniform", "simple", "ddim_uniform"]

DEFAULT_MAP = {
    'cfg': DEFAULT_CFG,
    'denoise': DEFAULT_DENOISE,
    'steps': DEFAULT_STEPS,
    'batch_size': DEFAULT_BATCH_SIZE,
    'height': DEFAULT_HEIGHT,
    'width': DEFAULT_WIDTH,
    'sampler': DEFAULT_SAMPLER_NAME,
    'scheduler': DEFAULT_SCHEDULER,
    'negative_prompt': DEFAULT_NEGATIVE_TEXT,
    'seed': random.randrange(1, 999999999)
}

SPEED_DICT = {
    'fast': '快',
    'normal': '一般',
    'slow': '慢',
}

DEFAULT_EXPIRE_TIME = 60 * 60 * 24  # 默认缓存一天

# 图片变化的程度配置
vary_denoise_map = {
    'subtle': 0.7,
    'strong': 0.9,
}
