{"3": {"inputs": {"seed": 1055445582807912, "steps": 25, "cfg": 5, "sampler_name": "euler_ancestral", "scheduler": "simple", "denoise": 1, "model": ["83", 0], "positive": ["15", 0], "negative": ["15", 1], "latent_image": ["76", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "4": {"inputs": {"ckpt_name": "animagineXL40_v4Opt.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "6": {"inputs": {"text": "A girl is in the city，anime style", "clip": ["83", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Positive"}}, "7": {"inputs": {"text": "(bad quality,worst quality,low quality,bad anatomy,bad hand:1.3), nsfw, lowres, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, username, blurry, artist name, moniors, little girls, young-age", "clip": ["83", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Negetive"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "9": {"inputs": {"filename_prefix": "ComfyUI", "images": ["8", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "15": {"inputs": {"strength": 0.5, "start_percent": 0, "end_percent": 1, "positive": ["6", 0], "negative": ["7", 0], "control_net": ["17", 0], "image": ["74", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet"}}, "16": {"inputs": {"control_net_name": "ContolnetPlus_promax.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "17": {"inputs": {"type": "canny/lineart/anime_lineart/mlsd", "control_net": ["16", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "SetUnionControlNetType"}}, "67": {"inputs": {"image": "b6fd7766b71767609dd3da284ddd56e8.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "74": {"inputs": {"merge_with_lineart": "lineart_standard", "resolution": 1280, "lineart_lower_bound": 0, "lineart_upper_bound": 1, "object_min_size": 36, "object_connectivity": 1, "image": ["67", 0]}, "class_type": "AnyLineArtPreprocessor_aux", "_meta": {"title": "AnyLine Lineart"}}, "75": {"inputs": {"images": ["74", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "76": {"inputs": {"width": ["80", 0], "height": ["80", 1], "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "80": {"inputs": {"image": ["67", 0]}, "class_type": "CM_NearestSDXLResolution", "_meta": {"title": "NearestSDXLResolution"}}, "81": {"inputs": {"lora_name": "LCMTurboMix_Euler_A_fix.safetensors", "strength_model": 0.3, "strength_clip": 1, "model": ["4", 0], "clip": ["4", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "82": {"inputs": {"lora_name": "ClearHand-V2.safetensors", "strength_model": 0.8, "strength_clip": 1, "model": ["81", 0], "clip": ["81", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "83": {"inputs": {"lora_name": "cfg_scale_boost.safetensors", "strength_model": 0.15, "strength_clip": 1, "model": ["82", 0], "clip": ["82", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}}