{"3": {"inputs": {"seed": 587393602030804, "steps": 25, "cfg": 4.5, "sampler_name": "dpmpp_2m_sde_gpu", "scheduler": "karras", "denoise": 1, "model": ["127", 0], "positive": ["15", 0], "negative": ["15", 1], "latent_image": ["116", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "4": {"inputs": {"ckpt_name": "PicLumen_HXL_Real_v2.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "6": {"inputs": {"text": "Anime style", "clip": ["127", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Positive"}}, "7": {"inputs": {"text": "text, watermark, (frame:1.8), person, people, illustration", "clip": ["127", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Negetive"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "10": {"inputs": {"left": 304, "top": 304, "right": 304, "bottom": 304, "feathering": 300, "image": ["11", 0]}, "class_type": "ImagePadForOutpaint", "_meta": {"title": "Pad Image for Outpainting"}}, "11": {"inputs": {"image": "5dd8310b3611f3938f5982ca8135382ccbf79fc5a842ef1cb4bb325801734ad9.webp", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "15": {"inputs": {"strength": 1, "start_percent": 0, "end_percent": 0.8, "positive": ["6", 0], "negative": ["7", 0], "control_net": ["17", 0], "image": ["49", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet"}}, "16": {"inputs": {"control_net_name": "ContolnetPlus_promax.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "17": {"inputs": {"type": "repaint", "control_net": ["16", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "SetUnionControlNetType"}}, "47": {"inputs": {"mask": ["48", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "48": {"inputs": {"mask": ["71", 0]}, "class_type": "InvertMask", "_meta": {"title": "InvertMask"}}, "49": {"inputs": {"x": 0, "y": 0, "resize_source": false, "destination": ["96", 0], "source": ["47", 0], "mask": ["71", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "64": {"inputs": {"fill": "telea", "falloff": 0, "image": ["96", 0], "mask": ["71", 0]}, "class_type": "INPAINT_MaskedFill", "_meta": {"title": "Fill Masked Area"}}, "71": {"inputs": {"width": ["124", 0], "height": ["124", 1], "keep_proportions": false, "mask": ["84", 0]}, "class_type": "ResizeMask", "_meta": {"title": "Resize Mask"}}, "74": {"inputs": {"width": 512, "height": 512, "upscale_method": "lanc<PERSON>s", "keep_proportion": false, "divisible_by": 2, "crop": "disabled", "image": ["8", 0], "get_image_size": ["10", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "83": {"inputs": {"masks": ["10", 1]}, "class_type": "Mask Ceiling Region", "_meta": {"title": "Mask Ceiling Region"}}, "84": {"inputs": {"threshold": 20, "mask": ["83", 0]}, "class_type": "ToBinaryMask", "_meta": {"title": "ToBinaryMask"}}, "96": {"inputs": {"width": 512, "height": 512, "upscale_method": "nearest-exact", "keep_proportion": false, "divisible_by": 2, "width_input": ["124", 0], "height_input": ["124", 1], "crop": "disabled", "image": ["10", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "102": {"inputs": {"image_from": ["10", 0], "image_to": ["74", 0], "mask": ["112", 0]}, "class_type": "ImageCompositeFromMaskBatch+", "_meta": {"title": "🔧 Image Composite From Mask Batch"}}, "111": {"inputs": {"mask": ["10", 1]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "112": {"inputs": {"channel": "red", "image": ["111", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "114": {"inputs": {"filename_prefix": "ComfyUI", "images": ["102", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "116": {"inputs": {"amount": 1, "samples": ["122", 0]}, "class_type": "RepeatLatentBatch", "_meta": {"title": "Repeat Latent Batch"}}, "122": {"inputs": {"pixels": ["64", 0], "vae": ["4", 2]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "124": {"inputs": {"image": ["10", 0]}, "class_type": "GetResolutionForVAE", "_meta": {"title": "GetResolutionForVAE"}}, "125": {"inputs": {"lora_name": "ClearHand-V2.safetensors", "strength_model": 0.8, "strength_clip": 1, "model": ["126", 0], "clip": ["126", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "126": {"inputs": {"lora_name": "LCMTurboMix_Euler_A_fix.safetensors", "strength_model": 0.3, "strength_clip": 1, "model": ["4", 0], "clip": ["4", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "127": {"inputs": {"lora_name": "cfg_scale_boost.safetensors", "strength_model": 0.15, "strength_clip": 1, "model": ["125", 0], "clip": ["125", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}}