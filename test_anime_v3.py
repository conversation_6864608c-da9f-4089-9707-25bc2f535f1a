#!/usr/bin/env python3
"""
测试 piclumen 动漫 V3 模型的 inpaint 和 outpaint 功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from comfy.defines import model_info_dict, workflow_map

def test_anime_v3_config():
    """测试动漫 V3 模型配置"""
    model_id = 'nj7hy6t8-reb0-7556-9ytu-567543ck7yh8'
    
    print("=== 测试 piclumen 动漫 V3 模型配置 ===")
    
    # 检查模型是否存在
    if model_id not in model_info_dict:
        print(f"❌ 模型 {model_id} 不存在")
        return False
    
    model_config = model_info_dict[model_id]
    print(f"✅ 模型存在: {model_config['model_display']}")
    print(f"   描述: {model_config['desc']}")
    print(f"   模型文件: {model_config['model']}")
    print(f"   MD5: {model_config['md5']}")
    
    # 检查基础生图工作流
    workflow_files = model_config.get('workflow_file', {})
    print(f"\n📁 工作流文件:")
    for key, file in workflow_files.items():
        print(f"   {key}: {file}")
    
    # 检查扩图配置
    enlarge_config = model_config.get('enlarge_img_config', {})
    print(f"\n🔧 扩图配置:")
    for key, value in enlarge_config.items():
        print(f"   {key}: {value}")
    
    # 检查重绘配置
    redraw_config = model_config.get('local_redraw_config', {})
    print(f"\n🎨 重绘配置:")
    for key, value in redraw_config.items():
        print(f"   {key}: {value}")
    
    # 检查默认配置
    default_config = model_config.get('default_config', {})
    print(f"\n⚙️  默认配置:")
    important_keys = ['steps', 'cfg', 'samplerName', 'scheduler', 'negativePrompt']
    for key in important_keys:
        if key in default_config:
            value = default_config[key]
            if key == 'negativePrompt' and len(str(value)) > 50:
                value = str(value)[:50] + "..."
            print(f"   {key}: {value}")
    
    return True

def test_workflow_mappings():
    """测试工作流映射"""
    print("\n=== 测试工作流映射 ===")
    
    # 检查 inpaint 工作流
    inpaint_key = 'anime_v3_inpaint'
    if inpaint_key in workflow_map:
        inpaint_config = workflow_map[inpaint_key]
        print(f"✅ {inpaint_key} 工作流存在")
        print(f"   类: {inpaint_config['obj'].__name__}")
        print(f"   文件: {inpaint_config['workflow_file']}")
        print(f"   描述: {inpaint_config['desc']}")
    else:
        print(f"❌ {inpaint_key} 工作流不存在")
        return False
    
    # 检查 outpaint 工作流
    outpaint_key = 'anime_v3_outpaint'
    if outpaint_key in workflow_map:
        outpaint_config = workflow_map[outpaint_key]
        print(f"✅ {outpaint_key} 工作流存在")
        print(f"   类: {outpaint_config['obj'].__name__}")
        print(f"   文件: {outpaint_config['workflow_file']}")
        print(f"   描述: {outpaint_config['desc']}")
    else:
        print(f"❌ {outpaint_key} 工作流不存在")
        return False
    
    return True

def test_workflow_classes():
    """测试工作流类是否可以正确导入"""
    print("\n=== 测试工作流类导入 ===")
    
    try:
        from comfy.apis import AnimeV3InpaintWorkflow, AnimeV3OutpaintWorkflow
        print("✅ AnimeV3InpaintWorkflow 导入成功")
        print("✅ AnimeV3OutpaintWorkflow 导入成功")
        
        # 检查类的基本方法
        inpaint_methods = [method for method in dir(AnimeV3InpaintWorkflow) if not method.startswith('_')]
        outpaint_methods = [method for method in dir(AnimeV3OutpaintWorkflow) if not method.startswith('_')]
        
        print(f"   AnimeV3InpaintWorkflow 方法: {', '.join(inpaint_methods)}")
        print(f"   AnimeV3OutpaintWorkflow 方法: {', '.join(outpaint_methods)}")
        
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试 piclumen 动漫 V3 模型功能...\n")
    
    success = True
    
    # 测试模型配置
    success &= test_anime_v3_config()
    
    # 测试工作流映射
    success &= test_workflow_mappings()
    
    # 测试工作流类
    success &= test_workflow_classes()
    
    print(f"\n{'='*50}")
    if success:
        print("🎉 所有测试通过！piclumen 动漫 V3 模型配置完成。")
        print("\n📋 功能总结:")
        print("   ✅ 基础生图功能 (anime_v3_base.json)")
        print("   ✅ 局部重绘功能 (anime_v3_inpaint.json)")
        print("   ✅ 扩图功能 (anime_v3_outpaint.json)")
        print("   ✅ 图生图功能支持")
        print("   ✅ 高清修复功能支持")
    else:
        print("❌ 部分测试失败，请检查配置。")
    
    return success

if __name__ == "__main__":
    main()
