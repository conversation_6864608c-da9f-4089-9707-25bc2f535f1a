#!/usr/bin/env python3
"""
最终测试 piclumen 动漫 V3 模型配置（使用标准 GenImageWorkflow）
"""

import json
import os

def test_workflow_files():
    """测试工作流文件是否存在且结构正确"""
    print("=== 测试工作流文件 ===")
    
    workflow_files = [
        'comfy/workflows/anime_v3_base.json',
        'comfy/workflows/anime_v3_inpaint.json', 
        'comfy/workflows/anime_v3_outpaint.json'
    ]
    
    for file_path in workflow_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path} 存在")
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                print(f"   - JSON 格式正确，包含 {len(data)} 个节点")
            except json.JSONDecodeError as e:
                print(f"   ❌ JSON 格式错误: {e}")
        else:
            print(f"❌ {file_path} 不存在")

def test_base_workflow_structure():
    """测试基础生图工作流是否符合 GenImageWorkflow 标准"""
    print("\n=== 测试基础生图工作流结构 ===")
    
    try:
        with open('comfy/workflows/anime_v3_base.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 检查标准节点
        required_nodes = {
            '3': 'KSampler',
            '4': 'CheckpointLoaderSimple', 
            '5': 'EmptyLatentImage',
            '6': 'CLIPTextEncode',  # 正向提示词
            '7': 'CLIPTextEncode'   # 负向提示词
        }
        
        all_correct = True
        for node_id, expected_type in required_nodes.items():
            if node_id in data:
                actual_type = data[node_id]['class_type']
                if expected_type in actual_type:
                    print(f"✅ 节点{node_id}: {actual_type}")
                else:
                    print(f"❌ 节点{node_id}: 期望包含 {expected_type}, 实际为 {actual_type}")
                    all_correct = False
            else:
                print(f"❌ 缺少节点{node_id}")
                all_correct = False
        
        if all_correct:
            print("✅ 基础生图工作流结构符合 GenImageWorkflow 标准")
        else:
            print("❌ 基础生图工作流结构不符合标准")
            
        return all_correct
        
    except Exception as e:
        print(f"❌ 检查基础生图工作流失败: {e}")
        return False

def test_defines_configuration():
    """测试 defines.py 配置"""
    print("\n=== 测试 defines.py 配置 ===")
    
    try:
        with open('comfy/defines.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否使用标准 GenImageWorkflow
        if "'obj': GenImageWorkflow" in content and "nj7hy6t8-reb0-7556-9ytu-567543ck7yh8" in content:
            print("✅ V3 模型使用标准 GenImageWorkflow")
        else:
            print("❌ V3 模型未使用标准 GenImageWorkflow")
        
        # 检查是否移除了自定义类引用
        if 'AnimeV3GenImageWorkflow' not in content:
            print("✅ 已移除不必要的 AnimeV3GenImageWorkflow 引用")
        else:
            print("❌ 仍然包含 AnimeV3GenImageWorkflow 引用")
        
        # 检查专用工作流映射
        if 'anime_v3_inpaint' in content and 'anime_v3_outpaint' in content:
            print("✅ V3 专用 inpaint/outpaint 工作流映射存在")
        else:
            print("❌ V3 专用工作流映射缺失")
        
        # 检查模型配置
        if 'PicLumen Anime V3' in content:
            print("✅ V3 模型显示名称正确")
        else:
            print("❌ V3 模型显示名称错误")
            
        if 'local_redraw_config' in content:
            print("✅ local_redraw_config 配置存在")
        else:
            print("❌ local_redraw_config 配置缺失")
            
    except Exception as e:
        print(f"❌ 检查 defines.py 失败: {e}")

def test_apis_classes():
    """测试 APIs 类"""
    print("\n=== 测试 APIs 类 ===")
    
    try:
        with open('comfy/apis.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否移除了不必要的类
        if 'class AnimeV3GenImageWorkflow' not in content:
            print("✅ 已移除不必要的 AnimeV3GenImageWorkflow 类")
        else:
            print("❌ 仍然包含 AnimeV3GenImageWorkflow 类")
        
        # 检查专用类是否存在
        if 'class AnimeV3InpaintWorkflow' in content:
            print("✅ AnimeV3InpaintWorkflow 类存在")
        else:
            print("❌ AnimeV3InpaintWorkflow 类缺失")
            
        if 'class AnimeV3OutpaintWorkflow' in content:
            print("✅ AnimeV3OutpaintWorkflow 类存在")
        else:
            print("❌ AnimeV3OutpaintWorkflow 类缺失")
            
    except Exception as e:
        print(f"❌ 检查 apis.py 失败: {e}")

def test_workflow_consistency():
    """测试工作流一致性"""
    print("\n=== 测试工作流一致性 ===")
    
    # 检查 inpaint 工作流
    try:
        with open('comfy/workflows/anime_v3_inpaint.json', 'r', encoding='utf-8') as f:
            inpaint_data = json.load(f)
        
        if '75' in inpaint_data and 'CheckpointLoader' in inpaint_data['75']['class_type']:
            print("✅ inpaint 工作流节点75为模型加载器")
        else:
            print("❌ inpaint 工作流节点75不是模型加载器")
            
        if '71' in inpaint_data and inpaint_data['71']['class_type'] == 'LoadImageMask':
            print("✅ inpaint 工作流节点71为遮罩加载器")
        else:
            print("❌ inpaint 工作流节点71不是遮罩加载器")
            
    except Exception as e:
        print(f"❌ 检查 inpaint 工作流失败: {e}")
    
    # 检查 outpaint 工作流
    try:
        with open('comfy/workflows/anime_v3_outpaint.json', 'r', encoding='utf-8') as f:
            outpaint_data = json.load(f)
        
        if '4' in outpaint_data and outpaint_data['4']['class_type'] == 'CheckpointLoaderSimple':
            print("✅ outpaint 工作流节点4为模型加载器")
        else:
            print("❌ outpaint 工作流节点4不是模型加载器")
            
        if '10' in outpaint_data and outpaint_data['10']['class_type'] == 'ImagePadForOutpaint':
            print("✅ outpaint 工作流节点10为扩图处理器")
        else:
            print("❌ outpaint 工作流节点10不是扩图处理器")
            
    except Exception as e:
        print(f"❌ 检查 outpaint 工作流失败: {e}")

def main():
    """主测试函数"""
    print("开始最终测试 piclumen 动漫 V3 模型配置...\n")
    
    # 测试工作流文件
    test_workflow_files()
    
    # 测试基础生图工作流结构
    base_structure_ok = test_base_workflow_structure()
    
    # 测试 defines.py 配置
    test_defines_configuration()
    
    # 测试 APIs 类
    test_apis_classes()
    
    # 测试工作流一致性
    test_workflow_consistency()
    
    print(f"\n{'='*60}")
    print("🎉 piclumen 动漫 V3 模型最终配置检查完成！")
    print("\n📋 修正后的实现方案:")
    print("   ✅ 基础生图: 使用标准 GenImageWorkflow + 标准化的 anime_v3_base.json")
    print("   ✅ 局部重绘: 使用专用 AnimeV3InpaintWorkflow + anime_v3_inpaint.json")
    print("   ✅ 扩图功能: 使用专用 AnimeV3OutpaintWorkflow + anime_v3_outpaint.json")
    print("   ✅ 移除了不必要的 AnimeV3GenImageWorkflow 类")
    print("   ✅ 工作流文件符合标准节点结构")
    
    if base_structure_ok:
        print("\n✨ 优势:")
        print("   - 复用现有的成熟代码")
        print("   - 保持代码一致性")
        print("   - 减少维护成本")
        print("   - 符合项目架构规范")
    else:
        print("\n⚠️  需要进一步调整基础生图工作流结构")

if __name__ == "__main__":
    main()
