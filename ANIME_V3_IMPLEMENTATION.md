# PicLumen 动漫 V3 模型实现总结

## 概述

本次实现为 PicLumen 动漫 V3 模型添加了完整的基础生图、inpaint（局部重绘）和 outpaint（扩图）功能。

## 实现的功能

### 1. 基础生图功能
- **工作流文件**: `anime_v3_base.json`
- **模型**: `animagineXL40_v4Opt.safetensors`
- **特点**: 支持高质量动漫风格图像生成

### 2. Inpaint（局部重绘）功能
- **工作流文件**: `anime_v3_inpaint.json`
- **API 类**: `AnimeV3InpaintWorkflow`
- **工作流映射**: `anime_v3_inpaint`
- **特点**: 
  - 使用 ControlNet Plus 进行精确重绘
  - 支持遮罩扩展和模糊处理
  - 集成多个 LoRA 模型优化效果

### 3. Outpaint（扩图）功能
- **工作流文件**: `anime_v3_outpaint.json`
- **API 类**: `AnimeV3OutpaintWorkflow`
- **工作流映射**: `anime_v3_outpaint`
- **特点**:
  - 智能图像扩展
  - 支持自定义扩展方向和大小
  - 无缝融合原图和扩展区域

## 修改的文件

### 1. `comfy/apis.py`
添加了两个新的工作流类：

#### AnimeV3InpaintWorkflow
```python
class AnimeV3InpaintWorkflow(Image2ImageWorkflow):
    """动漫 V3 局部重绘 (inpaint)"""
```
- 继承自 `Image2ImageWorkflow`
- 支持图片和遮罩上传
- 使用节点75加载模型
- 支持强度、去噪等参数调整

#### AnimeV3OutpaintWorkflow
```python
class AnimeV3OutpaintWorkflow(Image2ImageWorkflow):
    """动漫 V3 扩图 (outpaint)"""
```
- 继承自 `Image2ImageWorkflow`
- 支持四个方向的扩展（left, right, top, bottom）
- 使用节点4加载模型
- 支持自定义扩展参数

### 2. `comfy/defines.py`

#### 更新导入语句
```python
from comfy.apis import ..., AnimeV3InpaintWorkflow, AnimeV3OutpaintWorkflow
```

#### 添加工作流映射
```python
# 在 local_redraw_map 中添加
'anime_v3_inpaint': {
    'obj': AnimeV3InpaintWorkflow,
    'model': '',
    'workflow_file': 'anime_v3_inpaint.json',
    'desc': '动漫V3局部重绘'
}

# 在 enlarge_image_map 中添加
'anime_v3_outpaint': {
    'obj': AnimeV3OutpaintWorkflow,
    'model': '',
    'workflow_file': 'anime_v3_outpaint.json',
    'desc': '动漫V3扩图'
}
```

#### 完善 V3 模型配置
```python
'nj7hy6t8-reb0-7556-9ytu-567543ck7yh8': {
    'model_display': 'PicLumen Anime V3',
    'desc': 'Advanced anime model with enhanced quality and artistic style.',
    'md5': 'a2e7e60252d47a38f3a44612e4e050e3',
    
    # 扩图配置
    'enlarge_img_config': {
        'enlarge_type': "anime_v3_outpaint",
        'sampler_name': 'dpmpp_2m_sde_gpu',
        'scheduler': 'karras',
        'steps': 25,
        'cfg': 4.5,
        'negative_prompt': "text, watermark, (frame:1.8), person, people, illustration"
    },
    
    # 局部重绘配置
    'local_redraw_config': {
        'redraw_type': "anime_v3_inpaint",
        'sampler_name': 'euler_ancestral',
        'scheduler': 'simple',
        'steps': 25,
        'cfg': 7,
        'strength': 0.5,
        'negative_prompt': "..."
    }
}
```

## 技术细节

### 工作流节点映射

#### Inpaint 工作流 (anime_v3_inpaint.json)
- **节点75**: CheckpointLoaderSimpleShared - 模型加载
- **节点71**: LoadImageMask - 遮罩加载
- **节点11**: LoadImage - 原图加载
- **节点78**: 最终模型输出（经过 LoRA 链处理）

#### Outpaint 工作流 (anime_v3_outpaint.json)
- **节点4**: CheckpointLoaderSimple - 模型加载
- **节点11**: LoadImage - 原图加载
- **节点10**: ImagePadForOutpaint - 图像扩展
- **节点127**: 最终模型输出（经过 LoRA 链处理）

### 参数配置

#### 默认生图参数
- **采样器**: euler_ancestral
- **调度器**: simple
- **步数**: 20
- **CFG**: 3.0
- **分辨率**: 1024x1024

#### Inpaint 参数
- **采样器**: euler_ancestral
- **调度器**: simple
- **步数**: 25
- **CFG**: 7
- **强度**: 0.5

#### Outpaint 参数
- **采样器**: dpmpp_2m_sde_gpu
- **调度器**: karras
- **步数**: 25
- **CFG**: 4.5

## 使用方法

### 基础生图
使用现有的 `GenImageWorkflow` 类，工作流文件为 `anime_v3_base.json`

### Inpaint（局部重绘）
```python
workflow = AnimeV3InpaintWorkflow(
    extra_params={
        'img_url': '原图URL',
        'mask_img_url': '遮罩图URL',
        'prompt': '重绘提示词',
        'denoise': 0.8,  # 可选
        'local_redraw_model_id': 'nj7hy6t8-reb0-7556-9ytu-567543ck7yh8'
    }
)
```

### Outpaint（扩图）
```python
workflow = AnimeV3OutpaintWorkflow(
    extra_params={
        'img_url': '原图URL',
        'prompt': '扩图提示词',
        'left': 200,    # 左侧扩展像素
        'right': 200,   # 右侧扩展像素
        'top': 0,       # 顶部扩展像素
        'bottom': 0,    # 底部扩展像素
        'enlarge_image_model_id': 'nj7hy6t8-reb0-7556-9ytu-567543ck7yh8'
    }
)
```

## 测试验证

所有功能已通过测试验证：
- ✅ 工作流文件存在且格式正确
- ✅ API 类正确实现
- ✅ 工作流映射配置正确
- ✅ 节点引用一致性检查通过
- ✅ 模型配置完整

## 总结

PicLumen 动漫 V3 模型现在具备了完整的图像生成、编辑和扩展功能，可以满足用户的各种动漫风格图像处理需求。实现遵循了现有代码架构，保持了良好的兼容性和可维护性。
